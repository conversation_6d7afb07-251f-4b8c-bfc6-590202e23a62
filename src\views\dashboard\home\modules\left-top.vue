<script setup lang="ts">
import { h, ref } from 'vue';
import { NIcon } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

defineOptions({
  name: 'LeftTopContainer'
});

interface Props {
  title: string;
  icon: string;
  animationDelay?: number;
}

const props = withDefaults(defineProps<Props>(), {
  animationDelay: 0
});

// 指标数据接口
interface MetricItem {
  id: number;
  name: string;
  value: number | string;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'success' | 'warning' | 'danger';
  completionRate: number;
  lastUpdate: string;
}

// 模拟指标数据 - 6个指标用于3x2布局
const metrics = ref<MetricItem[]>([
  {
    id: 1,
    name: '销售完成率',
    value: 125.6,
    target: 100,
    unit: '%',
    trend: 'up',
    status: 'success',
    completionRate: 125.6,
    lastUpdate: '2024-01-15 14:30'
  },
  {
    id: 2,
    name: '客户满意度',
    value: 98.5,
    target: 95,
    unit: '%',
    trend: 'up',
    status: 'success',
    completionRate: 103.7,
    lastUpdate: '2024-01-15 14:25'
  },
  {
    id: 3,
    name: '订单转化率',
    value: 8.2,
    target: 10,
    unit: '%',
    trend: 'down',
    status: 'warning',
    completionRate: 82.0,
    lastUpdate: '2024-01-15 14:20'
  },
  {
    id: 4,
    name: '库存周转率',
    value: 3.8,
    target: 5,
    unit: '次',
    trend: 'down',
    status: 'danger',
    completionRate: 76.0,
    lastUpdate: '2024-01-15 14:15'
  },
  {
    id: 5,
    name: '用户活跃度',
    value: 89.3,
    target: 85,
    unit: '%',
    trend: 'up',
    status: 'success',
    completionRate: 105.1,
    lastUpdate: '2024-01-15 14:10'
  },
  {
    id: 6,
    name: '成本控制率',
    value: 92.1,
    target: 90,
    unit: '%',
    trend: 'stable',
    status: 'success',
    completionRate: 102.3,
    lastUpdate: '2024-01-15 14:05'
  }
]);

// 计算指标状态样式
const getMetricStatusStyle = (metric: MetricItem) => {
  const styles = {
    success: {
      color: '#52c41a',
      bgColor: 'rgba(82, 196, 26, 0.1)',
      borderColor: 'rgba(82, 196, 26, 0.3)'
    },
    warning: {
      color: '#faad14',
      bgColor: 'rgba(250, 173, 20, 0.1)',
      borderColor: 'rgba(250, 173, 20, 0.3)'
    },
    danger: {
      color: '#ff4d4f',
      bgColor: 'rgba(255, 77, 79, 0.1)',
      borderColor: 'rgba(255, 77, 79, 0.3)'
    }
  };
  return styles[metric.status];
};

// 计算达标状态
const getTargetStatus = (metric: MetricItem) => {
  const value = typeof metric.value === 'number' ? metric.value : Number.parseFloat(metric.value.toString());
  return value >= metric.target;
};

// 计算完成百分比
const getCompletionPercentage = (metric: MetricItem) => {
  const value = typeof metric.value === 'number' ? metric.value : Number.parseFloat(metric.value.toString());
  return Math.min((value / metric.target) * 100, 100);
};

// 表格列配置
const columns = ref([
  {
    key: 'name',
    title: '指标名称',
    align: 'left',
    minWidth: 120
  },
  {
    key: 'value',
    title: '当前值',
    align: 'center',
    minWidth: 100,
    render: (row: MetricItem) => {
      const style = getMetricStatusStyle(row);
      return h('span', { style: { color: style.color, fontWeight: '600' } }, `${row.value}${row.unit}`);
    }
  },
  {
    key: 'target',
    title: '目标值',
    align: 'center',
    minWidth: 100,
    render: (row: MetricItem) => `${row.target}${row.unit}`
  },
  {
    key: 'completionRate',
    title: '完成率',
    align: 'center',
    minWidth: 100,
    render: (row: MetricItem) => {
      const style = getMetricStatusStyle(row);
      return h('span', { style: { color: style.color, fontWeight: '600' } }, `${row.completionRate}%`);
    }
  },
  {
    key: 'status',
    title: '达标状态',
    align: 'center',
    minWidth: 100,
    render: (row: MetricItem) => {
      const isTarget = getTargetStatus(row);
      const color = isTarget ? '#52c41a' : '#ff4d4f';
      const bgColor = isTarget ? 'rgba(82, 196, 26, 0.15)' : 'rgba(255, 77, 79, 0.15)';
      return h(
        'span',
        {
          style: {
            color,
            backgroundColor: bgColor,
            padding: '4px 8px',
            borderRadius: '4px',
            fontSize: '12px',
            fontWeight: '500'
          }
        },
        isTarget ? '✓ 达标' : '✗ 未达标'
      );
    }
  },
  {
    key: 'trend',
    title: '趋势',
    align: 'center',
    minWidth: 80,
    render: (row: MetricItem) => {
      const style = getMetricStatusStyle(row);
      const iconMap = {
        up: 'mdi:trending-up',
        down: 'mdi:trending-down',
        stable: 'mdi:trending-neutral'
      };
      return h(
        NIcon,
        { size: 18, style: { color: style.color } },
        {
          default: () => h(SvgIcon, { icon: iconMap[row.trend] })
        }
      );
    }
  },
  {
    key: 'lastUpdate',
    title: '更新时间',
    align: 'center',
    minWidth: 140,
    render: (row: MetricItem) => h('span', { style: { color: '#ffffff80', fontSize: '12px' } }, row.lastUpdate)
  }
]);
</script>

<template>
  <DashboardCard
    :title="props.title"
    :title-icon="props.icon"
    height="100%"
    :animation-delay="props.animationDelay"
    :show-fullscreen="true"
    theme="blue"
  >
    <!-- 指标展示区域 - 3x2 网格布局 -->
    <div class="grid grid-cols-3 grid-rows-2 h-full gap-12px">
      <div
        v-for="metric in metrics"
        :key="metric.name"
        class="relative flex flex-col border rounded-8px p-12px transition-all duration-300 hover:scale-102"
        :style="{
          backgroundColor: getMetricStatusStyle(metric).bgColor,
          borderColor: getMetricStatusStyle(metric).borderColor
        }"
      >
        <!-- 指标头部 -->
        <div class="mb-8px flex items-center justify-between">
          <div class="flex items-center gap-6px">
            <!-- 状态指示器 -->
            <div
              class="h-6px w-6px animate-pulse rounded-full"
              :style="{ backgroundColor: getMetricStatusStyle(metric).color }"
            />
            <span class="truncate text-12px text-white/80 font-500">{{ metric.name }}</span>
          </div>

          <!-- 达标状态 -->
          <div
            class="rounded-3px px-4px py-1px text-8px font-500"
            :style="{
              color: getTargetStatus(metric) ? '#52c41a' : '#ff4d4f',
              backgroundColor: getTargetStatus(metric) ? 'rgba(82, 196, 26, 0.15)' : 'rgba(255, 77, 79, 0.15)'
            }"
          >
            {{ getTargetStatus(metric) ? '达标' : '未达标' }}
          </div>
        </div>

        <!-- 指标值 -->
        <div class="flex flex-col flex-1 justify-center">
          <div class="text-center">
            <div class="flex items-baseline justify-center gap-2px">
              <span class="text-18px font-700" :style="{ color: getMetricStatusStyle(metric).color }">
                {{ metric.value }}
              </span>
              <span class="text-10px text-white/50">{{ metric.unit }}</span>
            </div>
            <div class="mt-2px text-9px text-white/40">目标: {{ metric.target }}{{ metric.unit }}</div>
          </div>
        </div>

        <!-- 趋势和进度 -->
        <div class="mt-8px flex items-center justify-between">
          <div class="flex items-center gap-4px">
            <NIcon size="12" :style="{ color: getMetricStatusStyle(metric).color }">
              <SvgIcon
                :icon="
                  metric.trend === 'up'
                    ? 'mdi:trending-up'
                    : metric.trend === 'down'
                      ? 'mdi:trending-down'
                      : 'mdi:trending-neutral'
                "
              />
            </NIcon>
            <span class="text-8px text-white/50">
              {{ metric.trend === 'up' ? '上升' : metric.trend === 'down' ? '下降' : '稳定' }}
            </span>
          </div>

          <!-- 完成度进度条 -->
          <div class="w-24px">
            <div class="mb-1px text-right text-8px text-white/50">
              {{ Math.round(getCompletionPercentage(metric)) }}%
            </div>
            <div class="h-2px w-full rounded-1px bg-white/10">
              <div
                class="h-full rounded-1px transition-all duration-500"
                :style="{
                  width: `${getCompletionPercentage(metric)}%`,
                  backgroundColor: getMetricStatusStyle(metric).color
                }"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 全屏内容 - 表格展示 -->
    <template #fullscreen-content>
      <div class="h-full flex flex-col">
        <!-- 表格标题 -->
        <div class="mb-24px text-center">
          <div class="mb-12px text-4xl text-blue-400">📊</div>
          <div class="text-xl text-white/90">{{ props.title }} - 详细指标表格</div>
        </div>

        <!-- 数据表格 -->
        <div class="h-full w-full">
          <DataTable :columns="columns" :data="metrics" :row-key="(row: MetricItem) => row.id" class="h-full w-full" />
        </div>
      </div>
    </template>
  </DashboardCard>
</template>
